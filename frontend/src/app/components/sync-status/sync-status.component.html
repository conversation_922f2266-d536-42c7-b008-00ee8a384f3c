@if (syncStatus) {
<div
  class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 space-y-4"
>
  @if (showTitle) {
  <div
    class="flex items-center justify-between pb-3 border-b border-gray-200 dark:border-gray-700"
  >
    <div class="flex items-center space-x-2">
      <lucide-icon
        [img]="getStatusIcon()"
        class="w-5 h-5 text-blue-600"
      ></lucide-icon>
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
        Sync Status
      </h3>
    </div>
    <div
      class="flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      <lucide-icon [img]="getActionIcon()" class="w-4 h-4"></lucide-icon>
      <span>{{ getActionLabel() }}</span>
    </div>
  </div>
  }

  <!-- Progress Section -->
  <div class="space-y-3">
    <div class="flex items-center justify-between">
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
        >Progress</span
      >
      <span class="text-sm font-semibold text-gray-900 dark:text-white"
        >{{ getProgressValue().toFixed(1) }}%</span
      >
    </div>
    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
      <div
        class="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
        [style.width.%]="getProgressValue()"
      ></div>
    </div>
    <div
      class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400"
    >
      <div class="flex items-center space-x-1">
        <lucide-icon [img]="ZapIcon" class="w-3 h-3"></lucide-icon>
        <span>{{ syncStatus.speed }}</span>
      </div>
      <div class="flex items-center space-x-1">
        <lucide-icon [img]="ClockIcon" class="w-3 h-3"></lucide-icon>
        <span>{{ syncStatus.eta }}</span>
      </div>
      <div class="flex items-center space-x-1">
        <lucide-icon [img]="TimerIcon" class="w-3 h-3"></lucide-icon>
        <span>{{ syncStatus.elapsed_time }}</span>
      </div>
    </div>
  </div>

  <!-- Transfer Statistics -->
  @if (hasTransferData()) {
  <div class="space-y-3">
    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
      Transfer Statistics
    </h4>
    <div class="grid grid-cols-2 gap-3">
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="FileTextIcon"
            class="w-5 h-5 text-blue-500"
          ></lucide-icon>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              Files
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ syncStatus.files_transferred }}
              @if (syncStatus.total_files > 0) { /
              {{ syncStatus.total_files }} }
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="HardDriveIcon"
            class="w-5 h-5 text-green-500"
          ></lucide-icon>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              Data
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ formatBytes(syncStatus.bytes_transferred) }}
              @if (syncStatus.total_bytes > 0) { /
              {{ formatBytes(syncStatus.total_bytes) }} }
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  }

  <!-- Activity Statistics -->
  @if (hasActivityData()) {
  <div class="space-y-3">
    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
      Activity
    </h4>
    <div class="grid grid-cols-2 gap-3">
      @if (syncStatus.checks > 0) {
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="CheckIcon"
            class="w-5 h-5 text-blue-500"
          ></lucide-icon>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              Checks
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ syncStatus.checks }}
            </div>
          </div>
        </div>
      </div>
      } @if (syncStatus.deletes > 0) {
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="Trash2Icon"
            class="w-5 h-5 text-red-500"
          ></lucide-icon>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              Deletes
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ syncStatus.deletes }}
            </div>
          </div>
        </div>
      </div>
      } @if (syncStatus.errors > 0) {
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
        <div class="flex items-center space-x-2">
          <lucide-icon
            [img]="AlertCircleIcon"
            class="w-5 h-5 text-red-500"
          ></lucide-icon>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              Errors
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400">
              {{ syncStatus.errors }}
            </div>
          </div>
        </div>
      </div>
      }
    </div>
  </div>
  }

  <!-- Current File -->
  @if (syncStatus.current_file) {
  <div class="space-y-2">
    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">
      Current File
    </h4>
    <div
      class="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
    >
      <lucide-icon
        [img]="FolderOpenIcon"
        class="w-4 h-4 text-blue-500"
      ></lucide-icon>
      <span class="text-sm text-gray-900 dark:text-white truncate">{{
        syncStatus.current_file
      }}</span>
    </div>
  </div>
  }
</div>
} @else {
<div
  class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4"
>
  <div class="text-center py-8">
    <lucide-icon
      [img]="MoonIcon"
      class="w-12 h-12 mx-auto mb-4 text-gray-400"
    ></lucide-icon>
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
      Ready to Sync
    </h3>
    <p class="text-sm text-gray-600 dark:text-gray-400">
      No sync operation running
    </p>
  </div>
</div>
}
